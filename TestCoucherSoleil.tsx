import React, { useState } from 'react';

// Composant de test pour visualiser les nouvelles couleurs de coucher de soleil
const TestCoucherSoleil: React.FC = () => {
  const [selectedHour, setSelectedHour] = useState(18);

  // Palette de couleurs pour le coucher de soleil (copiée depuis DynamicBackground.tsx)
  const coucherSoleilColors = {
    17: { primary: '#FAA0A0', secondary: '#D8BFD8', tertiary: '#E5E5FA' }, // Transition avant
    18: { primary: '#ffb937', secondary: '#f17133', tertiary: '#654b62' }, // 18h05 - Début coucher
    19: { primary: '#f17133', secondary: '#b93d23', tertiary: '#553753' }, // 19h - Intensification
    20: { primary: '#b2856e', secondary: '#b93d23', tertiary: '#553753' }, // 20h - Fin coucher
    21: { primary: '#6A5ACD', secondary: '#483D8B', tertiary: '#2F2F4F' }  // Transition après
  };

  // Codes couleurs de votre palette photographique pour référence
  const paletteReference = {
    violet_fonce: '#553753',
    violet_moyen: '#654b62', 
    rouge_brique: '#b93d23',
    orange_vif: '#f17133',
    jaune_dore: '#ffb937',
    beige_chaud: '#b2856e'
  };

  const currentColors = coucherSoleilColors[selectedHour as keyof typeof coucherSoleilColors];
  const gradient = `linear-gradient(to top, ${currentColors.primary} 25%, ${currentColors.secondary} 50%, ${currentColors.tertiary} 85%)`;

  return (
    <div className="fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg p-6 max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <h2 className="text-2xl font-bold mb-4 text-center">🌅 Test Palette Coucher de Soleil</h2>
        
        {/* Sélecteur d'heure */}
        <div className="mb-6">
          <label className="block text-sm font-medium mb-2">Heure à tester :</label>
          <div className="flex gap-2 flex-wrap">
            {Object.keys(coucherSoleilColors).map(hour => (
              <button
                key={hour}
                onClick={() => setSelectedHour(parseInt(hour))}
                className={`px-3 py-2 rounded ${
                  selectedHour === parseInt(hour)
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-200 hover:bg-gray-300'
                }`}
              >
                {hour}h
              </button>
            ))}
          </div>
        </div>

        {/* Aperçu du dégradé */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-2">Aperçu du ciel à {selectedHour}h :</h3>
          <div
            className="w-full h-64 rounded-lg border-2 border-gray-300"
            style={{ background: gradient }}
          >
            <div className="h-full flex flex-col justify-between p-4 text-white text-sm font-medium">
              <div className="text-center bg-black bg-opacity-30 rounded px-2 py-1">
                🌌 Haut du ciel: {currentColors.tertiary}
              </div>
              <div className="text-center bg-black bg-opacity-30 rounded px-2 py-1">
                🌤️ Milieu: {currentColors.secondary}
              </div>
              <div className="text-center bg-black bg-opacity-30 rounded px-2 py-1">
                🌅 Horizon: {currentColors.primary}
              </div>
            </div>
          </div>
        </div>

        {/* Détails des couleurs */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-2">Détails des couleurs :</h3>
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center">
              <div 
                className="w-full h-16 rounded mb-2 border"
                style={{ backgroundColor: currentColors.primary }}
              ></div>
              <p className="text-sm font-medium">Horizon</p>
              <p className="text-xs text-gray-600">{currentColors.primary}</p>
            </div>
            <div className="text-center">
              <div 
                className="w-full h-16 rounded mb-2 border"
                style={{ backgroundColor: currentColors.secondary }}
              ></div>
              <p className="text-sm font-medium">Milieu</p>
              <p className="text-xs text-gray-600">{currentColors.secondary}</p>
            </div>
            <div className="text-center">
              <div 
                className="w-full h-16 rounded mb-2 border"
                style={{ backgroundColor: currentColors.tertiary }}
              ></div>
              <p className="text-sm font-medium">Haut</p>
              <p className="text-xs text-gray-600">{currentColors.tertiary}</p>
            </div>
          </div>
        </div>

        {/* Palette de référence */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-2">Palette photographique de référence :</h3>
          <div className="grid grid-cols-6 gap-2">
            {Object.entries(paletteReference).map(([name, color]) => (
              <div key={name} className="text-center">
                <div 
                  className="w-full h-12 rounded mb-1 border"
                  style={{ backgroundColor: color }}
                ></div>
                <p className="text-xs">{name.replace('_', ' ')}</p>
                <p className="text-xs text-gray-600">{color}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Bouton fermer */}
        <div className="text-center">
          <button
            onClick={() => window.location.reload()}
            className="bg-red-500 hover:bg-red-600 text-white px-6 py-2 rounded"
          >
            Fermer le test
          </button>
        </div>
      </div>
    </div>
  );
};

export default TestCoucherSoleil;
