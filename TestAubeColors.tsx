import React, { useState } from 'react';

// Composant de test pour visualiser les couleurs de l'aube
const TestAubeColors: React.FC = () => {
  const [selectedHour, setSelectedHour] = useState(5);

  // Palette de couleurs pour l'aube (copiée depuis DynamicBackground.tsx)
  const aubeColors = {
    5: { primary: '#ce6a6b', secondary: '#bed3c3', tertiary: '#212e53' },
    6: { primary: '#ebaca2', secondary: '#4a919e', tertiary: '#212e53' },
    7: { primary: '#ebaca2', secondary: '#4a919e', tertiary: '#212e53' },
    8: { primary: '#D4E6F1', secondary: '#85C1E9', tertiary: '#5DADE2' }
  };

  const currentColors = aubeColors[selectedHour as keyof typeof aubeColors];
  const gradient = `linear-gradient(to top, ${currentColors.primary} 25%, ${currentColors.secondary} 50%, ${currentColors.tertiary} 85%)`;

  return (
    <div className="fixed top-4 left-4 z-50 bg-black/80 backdrop-blur-sm rounded-lg p-4 text-white max-w-sm">
      <h3 className="text-lg font-bold mb-3">🌅 Test Couleurs Aube</h3>
      
      <div className="mb-4">
        <label className="block text-sm mb-2">Heure sélectionnée: {selectedHour}h</label>
        <div className="flex gap-2">
          {[5, 6, 7, 8].map(hour => (
            <button
              key={hour}
              onClick={() => setSelectedHour(hour)}
              className={`px-3 py-1 rounded text-sm ${
                selectedHour === hour 
                  ? 'bg-blue-600 text-white' 
                  : 'bg-gray-600 hover:bg-gray-500'
              }`}
            >
              {hour}h
            </button>
          ))}
        </div>
      </div>

      {/* Aperçu du dégradé */}
      <div className="mb-4">
        <p className="text-sm mb-2">Aperçu du dégradé:</p>
        <div 
          className="w-full h-24 rounded border-2 border-gray-600"
          style={{ background: gradient }}
        />
      </div>

      {/* Détails des couleurs */}
      <div className="text-xs space-y-1">
        <div className="flex items-center gap-2">
          <div 
            className="w-4 h-4 rounded border border-gray-400"
            style={{ backgroundColor: currentColors.primary }}
          />
          <span>Primary (bas): {currentColors.primary}</span>
        </div>
        <div className="flex items-center gap-2">
          <div 
            className="w-4 h-4 rounded border border-gray-400"
            style={{ backgroundColor: currentColors.secondary }}
          />
          <span>Secondary (milieu): {currentColors.secondary}</span>
        </div>
        <div className="flex items-center gap-2">
          <div 
            className="w-4 h-4 rounded border border-gray-400"
            style={{ backgroundColor: currentColors.tertiary }}
          />
          <span>Tertiary (haut): {currentColors.tertiary}</span>
        </div>
      </div>
    </div>
  );
};

export default TestAubeColors;
