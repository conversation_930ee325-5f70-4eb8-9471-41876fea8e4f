import React, { useState } from 'react';
import { useTime } from './TimeContext';

// Composant simple pour tester rapidement les heures d'aube
const QuickTimeTest: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);

  // Fonction pour forcer une heure spécifique (temporaire pour test)
  const testHour = (hour: number) => {
    // Créer une date avec l'heure spécifiée
    const testDate = new Date();
    testDate.setHours(hour, 0, 0, 0);
    
    // Temporairement modifier l'heure système pour le test
    // Note: Ceci est juste pour visualiser, pas pour production
    console.log(`🕐 Test: Simulation de ${hour}h pour voir les couleurs d'aube`);
    
    // Forcer un rechargement de la page avec l'heure modifiée
    // (méthode simple pour test rapide)
    window.location.reload();
  };

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className="fixed top-4 right-4 z-50 bg-orange-600/80 backdrop-blur-sm rounded-lg p-2 text-white text-sm hover:bg-orange-700/80"
      >
        🌅 Test Aube
      </button>
    );
  }

  return (
    <div className="fixed top-4 right-4 z-50 bg-orange-600/90 backdrop-blur-sm rounded-lg p-4 text-white">
      <div className="flex justify-between items-center mb-3">
        <h3 className="font-bold text-sm">🌅 Test Couleurs Aube</h3>
        <button
          onClick={() => setIsVisible(false)}
          className="text-orange-200 hover:text-white"
        >
          ✕
        </button>
      </div>

      <div className="text-xs mb-3">
        <p>Cliquez pour voir les couleurs:</p>
      </div>

      <div className="grid grid-cols-2 gap-2">
        <button 
          onClick={() => testHour(5)}
          className="px-3 py-2 bg-red-600 hover:bg-red-700 rounded text-xs"
        >
          5h - Aube Rouge
        </button>
        <button 
          onClick={() => testHour(6)}
          className="px-3 py-2 bg-pink-600 hover:bg-pink-700 rounded text-xs"
        >
          6h - Aube Rose
        </button>
        <button 
          onClick={() => testHour(7)}
          className="px-3 py-2 bg-teal-600 hover:bg-teal-700 rounded text-xs"
        >
          7h - Aube Teal
        </button>
        <button 
          onClick={() => testHour(8)}
          className="px-3 py-2 bg-blue-600 hover:bg-blue-700 rounded text-xs"
        >
          8h - Matin
        </button>
      </div>

      <div className="mt-3 text-xs text-orange-200">
        <p>Note: Rechargera la page pour voir l'effet</p>
      </div>
    </div>
  );
};

export default QuickTimeTest;
