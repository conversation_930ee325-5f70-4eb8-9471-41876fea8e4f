import React, { useState, useEffect } from 'react';
import * as SunCalc from 'suncalc';

// Composant de test pour vérifier le déclenchement automatique des étoiles
const TestEtoilesAutomatiques: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [testResults, setTestResults] = useState<any[]>([]);

  // Position par défaut (Paris)
  const userLocation = { lat: 48.8566, lon: 2.3522 };

  // Fonction identique à celle d'AstronomicalLayer pour calculer l'opacité des étoiles
  const calculateStarsOpacity = (currentTime: Date): number => {
    const sunTimes = SunCalc.getTimes(currentTime, userLocation.lat, userLocation.lon);
    
    const sunrise = sunTimes.sunrise.getHours() + sunTimes.sunrise.getMinutes() / 60;
    const sunset = sunTimes.sunset.getHours() + sunTimes.sunset.getMinutes() / 60;
    const nauticalDusk = sunTimes.nauticalDusk.getHours() + sunTimes.nauticalDusk.getMinutes() / 60;
    const nauticalDawn = sunTimes.nauticalDawn.getHours() + sunTimes.nauticalDawn.getMinutes() / 60;
    
    const currentHour = currentTime.getHours() + currentTime.getMinutes() / 60 + currentTime.getSeconds() / 3600;

    // Période de jour complet : pas d'étoiles visibles
    if (currentHour >= sunrise && currentHour <= sunset) {
      return 0;
    }

    // CRÉPUSCULE DU SOIR - Transition progressive après le coucher du soleil
    if (currentHour > sunset && currentHour <= nauticalDusk) {
      const progress = (currentHour - sunset) / (nauticalDusk - sunset);
      return progress * 0.6;
    }

    // DÉBUT DE NUIT - Transition vers la nuit complète
    if (currentHour > nauticalDusk && currentHour <= nauticalDusk + 0.75) {
      const progress = (currentHour - nauticalDusk) / 0.75;
      return 0.6 + (progress * 0.4);
    }

    // NUIT COMPLÈTE - Toutes les étoiles visibles
    if (currentHour > nauticalDusk + 0.75 && currentHour < nauticalDawn - 0.75) {
      return 1.0;
    }

    // FIN DE NUIT - Transition avant l'aube
    if (currentHour >= nauticalDawn - 0.75 && currentHour < nauticalDawn) {
      const progress = (currentHour - (nauticalDawn - 0.75)) / 0.75;
      return 1.0 - (progress * 0.4);
    }

    // CRÉPUSCULE DU MATIN - Transition progressive avant le lever du soleil
    if (currentHour >= nauticalDawn && currentHour < sunrise) {
      const progress = (currentHour - nauticalDawn) / (sunrise - nauticalDawn);
      return 0.6 * (1 - progress);
    }

    return 0;
  };

  // Tester différentes heures de la journée
  const runStarsTest = () => {
    const results = [];
    const testDate = new Date();
    
    // Tester toutes les heures de 16h à 24h (période critique)
    for (let hour = 16; hour <= 23; hour++) {
      for (let minute = 0; minute < 60; minute += 15) { // Toutes les 15 minutes
        const testTime = new Date(testDate);
        testTime.setHours(hour, minute, 0, 0);
        
        const opacity = calculateStarsOpacity(testTime);
        const sunTimes = SunCalc.getTimes(testTime, userLocation.lat, userLocation.lon);
        
        results.push({
          time: `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`,
          opacity: opacity,
          status: opacity === 0 ? 'JOUR' : opacity < 0.3 ? 'CRÉPUSCULE' : opacity < 0.8 ? 'DÉBUT NUIT' : 'NUIT COMPLÈTE',
          sunset: sunTimes.sunset.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' }),
          nauticalDusk: sunTimes.nauticalDusk.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })
        });
      }
    }
    
    setTestResults(results);
  };

  // Mettre à jour l'heure actuelle toutes les secondes
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    
    return () => clearInterval(interval);
  }, []);

  // Calculer l'opacité actuelle des étoiles
  const currentStarsOpacity = calculateStarsOpacity(currentTime);
  const sunTimes = SunCalc.getTimes(currentTime, userLocation.lat, userLocation.lon);

  if (!isVisible) {
    return (
      <button
        onClick={() => {
          setIsVisible(true);
          runStarsTest();
        }}
        className="fixed bottom-4 right-4 bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg z-50 text-sm"
      >
        🌟 Test Étoiles Auto
      </button>
    );
  }

  return (
    <div className="fixed inset-0 z-50 bg-black bg-opacity-75 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg p-6 max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <h2 className="text-2xl font-bold mb-4 text-center">🌟 Test Déclenchement Automatique des Étoiles</h2>
        
        {/* État actuel */}
        <div className="mb-6 p-4 bg-gray-100 rounded-lg">
          <h3 className="text-lg font-semibold mb-2">État Actuel :</h3>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p><strong>Heure :</strong> {currentTime.toLocaleTimeString('fr-FR')}</p>
              <p><strong>Coucher de soleil :</strong> {sunTimes.sunset.toLocaleTimeString('fr-FR')}</p>
              <p><strong>Crépuscule nautique :</strong> {sunTimes.nauticalDusk.toLocaleTimeString('fr-FR')}</p>
            </div>
            <div>
              <p><strong>Opacité étoiles :</strong> 
                <span className={`ml-2 px-2 py-1 rounded text-white ${
                  currentStarsOpacity === 0 ? 'bg-yellow-500' : 
                  currentStarsOpacity < 0.3 ? 'bg-orange-500' : 
                  currentStarsOpacity < 0.8 ? 'bg-blue-500' : 'bg-purple-500'
                }`}>
                  {(currentStarsOpacity * 100).toFixed(0)}%
                </span>
              </p>
              <p><strong>État :</strong> 
                <span className="ml-2 font-medium">
                  {currentStarsOpacity === 0 ? '☀️ JOUR' : 
                   currentStarsOpacity < 0.3 ? '🌅 CRÉPUSCULE' : 
                   currentStarsOpacity < 0.8 ? '🌌 DÉBUT NUIT' : '🌙 NUIT COMPLÈTE'}
                </span>
              </p>
            </div>
          </div>
        </div>

        {/* Résultats du test */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-2">Test Automatique (16h-23h) :</h3>
          <div className="max-h-64 overflow-y-auto border rounded">
            <table className="w-full text-sm">
              <thead className="bg-gray-50 sticky top-0">
                <tr>
                  <th className="px-3 py-2 text-left">Heure</th>
                  <th className="px-3 py-2 text-left">Opacité</th>
                  <th className="px-3 py-2 text-left">État</th>
                  <th className="px-3 py-2 text-left">Coucher</th>
                  <th className="px-3 py-2 text-left">Nautique</th>
                </tr>
              </thead>
              <tbody>
                {testResults.map((result, index) => (
                  <tr key={index} className={index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}>
                    <td className="px-3 py-2 font-mono">{result.time}</td>
                    <td className="px-3 py-2">
                      <span className={`px-2 py-1 rounded text-white text-xs ${
                        result.opacity === 0 ? 'bg-yellow-500' : 
                        result.opacity < 0.3 ? 'bg-orange-500' : 
                        result.opacity < 0.8 ? 'bg-blue-500' : 'bg-purple-500'
                      }`}>
                        {(result.opacity * 100).toFixed(0)}%
                      </span>
                    </td>
                    <td className="px-3 py-2 text-xs">{result.status}</td>
                    <td className="px-3 py-2 text-xs">{result.sunset}</td>
                    <td className="px-3 py-2 text-xs">{result.nauticalDusk}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Légende */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-2">Légende :</h3>
          <div className="grid grid-cols-2 gap-2 text-sm">
            <div className="flex items-center">
              <span className="w-4 h-4 bg-yellow-500 rounded mr-2"></span>
              <span>0% = Jour (pas d'étoiles)</span>
            </div>
            <div className="flex items-center">
              <span className="w-4 h-4 bg-orange-500 rounded mr-2"></span>
              <span>1-30% = Crépuscule (quelques étoiles)</span>
            </div>
            <div className="flex items-center">
              <span className="w-4 h-4 bg-blue-500 rounded mr-2"></span>
              <span>31-80% = Début de nuit (plus d'étoiles)</span>
            </div>
            <div className="flex items-center">
              <span className="w-4 h-4 bg-purple-500 rounded mr-2"></span>
              <span>81-100% = Nuit complète (toutes les étoiles)</span>
            </div>
          </div>
        </div>

        {/* Boutons */}
        <div className="text-center space-x-4">
          <button
            onClick={runStarsTest}
            className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded"
          >
            🔄 Relancer le test
          </button>
          <button
            onClick={() => setIsVisible(false)}
            className="bg-red-500 hover:bg-red-600 text-white px-6 py-2 rounded"
          >
            Fermer
          </button>
        </div>
      </div>
    </div>
  );
};

export default TestEtoilesAutomatiques;
